"""
API endpoints for invoice integrations management.
"""

from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Dict, Any
from uuid import UUID
import logging

from app.database import get_db
from app.models.integration import InvoiceIntegration, OAuth2Token
from app.models.tenant import Tenant
from app.middleware import get_current_tenant_user
from app.utils.permissions import Permission, check_permission
from app.utils.oauth2 import OAuth2Manager
from app.schemas.integration import (
    InvoiceIntegrationCreate, InvoiceIntegrationUpdate, InvoiceIntegrationResponse,
    AvailableIntegration, ConnectionTestResult, OAuth2LoginRequest, OAuth2LoginResponse,
    OAuth2CallbackRequest, IntegrationType, IntegrationSyncResult, ScheduleSettings, ScheduleSettingsUpdate
)
from app.integrations import InvoiceFetcher, FortnoxInvoiceFetcher, VismaInvoiceFetcher, SimpleHttpInvoiceFetcher
from app.services.integration_service import IntegrationService
from app.tasks.ai_agent_tasks import ai_agent_fetch_invoices_task

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/available", response_model=List[AvailableIntegration])
async def get_available_integrations():
    """Get list of available integration types"""
    return [
        AvailableIntegration(
            type=IntegrationType.FORTNOX,
            name="Fortnox",
            description="Swedish accounting software with OAuth2 authentication",
            oauth_required=True,
            config_fields=[
                {"name": "client_id", "type": "string", "required": True, "description": "Fortnox Client ID"},
                {"name": "client_secret", "type": "string", "required": True, "description": "Fortnox Client Secret", "sensitive": True}
            ]
        ),
        AvailableIntegration(
            type=IntegrationType.VISMA,
            name="Visma eEkonomi",
            description="Visma eEkonomi accounting software with OAuth2 authentication",
            oauth_required=True,
            config_fields=[
                {"name": "client_id", "type": "string", "required": True, "description": "Visma Client ID"},
                {"name": "client_secret", "type": "string", "required": True, "description": "Visma Client Secret", "sensitive": True}
            ]
        ),
        AvailableIntegration(
            type=IntegrationType.HTTP,
            name="HTTP/HTTPS",
            description="Direct HTTP/HTTPS file download integration",
            oauth_required=False,
            config_fields=[
                {"name": "base_url", "type": "url", "required": True, "description": "Base URL for invoice files"},
                {"name": "auth_type", "type": "select", "required": False, "description": "Authentication type", 
                 "options": ["none", "basic", "bearer", "api_key"]},
                {"name": "username", "type": "string", "required": False, "description": "Username for basic auth"},
                {"name": "password", "type": "string", "required": False, "description": "Password for basic auth", "sensitive": True},
                {"name": "api_key", "type": "string", "required": False, "description": "API key for authentication", "sensitive": True}
            ]
        )
    ]


@router.post("/setup", response_model=InvoiceIntegrationResponse)
async def setup_integration(
    integration_data: InvoiceIntegrationCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_tenant_user)
):
    """Setup a new invoice integration"""
    # Check permissions
    check_permission(current_user, Permission.MANAGE_INTEGRATIONS)
    
    # Validate configuration based on integration type
    _validate_integration_config(integration_data.integration_type, integration_data.configuration)
    
    # Create integration
    integration = InvoiceIntegration(
        tenant_id=current_user.tenant_id,
        integration_type=integration_data.integration_type.value,
        configuration=integration_data.configuration,
        name=integration_data.name,
        description=integration_data.description,
        is_active=integration_data.is_active
    )
    
    db.add(integration)
    db.commit()
    db.refresh(integration)
    
    logger.info(f"Created integration {integration.id} for tenant {current_user.tenant_id}")
    
    return integration


@router.get("/", response_model=List[InvoiceIntegrationResponse])
async def list_integrations(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_tenant_user)
):
    """List all integrations for current tenant"""
    integrations = db.query(InvoiceIntegration).filter(
        InvoiceIntegration.tenant_id == current_user.tenant_id
    ).all()
    
    return integrations


@router.get("/{integration_id}", response_model=InvoiceIntegrationResponse)
async def get_integration(
    integration_id: UUID,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_tenant_user)
):
    """Get specific integration"""
    integration = db.query(InvoiceIntegration).filter(
        InvoiceIntegration.id == integration_id,
        InvoiceIntegration.tenant_id == current_user.tenant_id
    ).first()
    
    if not integration:
        raise HTTPException(status_code=404, detail="Integration not found")
    
    return integration


@router.put("/{integration_id}", response_model=InvoiceIntegrationResponse)
async def update_integration(
    integration_id: UUID,
    update_data: InvoiceIntegrationUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_tenant_user)
):
    """Update integration configuration"""
    check_permission(current_user, Permission.MANAGE_INTEGRATIONS)
    
    integration = db.query(InvoiceIntegration).filter(
        InvoiceIntegration.id == integration_id,
        InvoiceIntegration.tenant_id == current_user.tenant_id
    ).first()
    
    if not integration:
        raise HTTPException(status_code=404, detail="Integration not found")
    
    # Update fields
    if update_data.configuration is not None:
        _validate_integration_config(IntegrationType(integration.integration_type), update_data.configuration)
        integration.configuration = update_data.configuration
    
    if update_data.name is not None:
        integration.name = update_data.name
    
    if update_data.description is not None:
        integration.description = update_data.description
    
    if update_data.is_active is not None:
        integration.is_active = update_data.is_active
    
    db.commit()
    db.refresh(integration)
    
    return integration


@router.delete("/{integration_id}")
async def delete_integration(
    integration_id: UUID,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_tenant_user)
):
    """Delete integration"""
    check_permission(current_user, Permission.MANAGE_INTEGRATIONS)
    
    integration = db.query(InvoiceIntegration).filter(
        InvoiceIntegration.id == integration_id,
        InvoiceIntegration.tenant_id == current_user.tenant_id
    ).first()
    
    if not integration:
        raise HTTPException(status_code=404, detail="Integration not found")
    
    db.delete(integration)
    db.commit()
    
    return {"message": "Integration deleted successfully"}


@router.post("/{integration_id}/test-connection", response_model=ConnectionTestResult)
async def test_integration_connection(
    integration_id: UUID,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_tenant_user)
):
    """Test connection to integration"""
    integration = db.query(InvoiceIntegration).filter(
        InvoiceIntegration.id == integration_id,
        InvoiceIntegration.tenant_id == current_user.tenant_id
    ).first()
    
    if not integration:
        raise HTTPException(status_code=404, detail="Integration not found")
    
    try:
        # Create fetcher instance
        fetcher = _create_fetcher(integration, db)
        
        # Test connection
        result = await fetcher.test_connection()
        
        return ConnectionTestResult(
            success=result["success"],
            message=result["message"],
            details=result.get("details"),
            tested_at=result["details"]["tested_at"] if "details" in result else None
        )
        
    except Exception as e:
        logger.error(f"Error testing integration {integration_id}: {e}")
        return ConnectionTestResult(
            success=False,
            message=f"Connection test failed: {str(e)}",
            tested_at=None
        )


@router.post("/oauth/{provider}/login", response_model=OAuth2LoginResponse)
async def oauth_login(
    provider: str,
    login_request: OAuth2LoginRequest,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_tenant_user)
):
    """Initiate OAuth2 login flow"""
    provider = provider.upper()
    if provider not in ["FORTNOX", "VISMA"]:
        raise HTTPException(status_code=400, detail="Unsupported OAuth provider")
    
    integration = db.query(InvoiceIntegration).filter(
        InvoiceIntegration.id == login_request.integration_id,
        InvoiceIntegration.tenant_id == current_user.tenant_id,
        InvoiceIntegration.integration_type == provider
    ).first()
    
    if not integration:
        raise HTTPException(status_code=404, detail="Integration not found")
    
    try:
        oauth_manager = OAuth2Manager(db)
        
        # Default redirect URI if not provided
        redirect_uri = login_request.redirect_uri or f"http://localhost:3000/integrations/oauth/{provider.lower()}/callback"
        
        auth_url, state = await oauth_manager.create_authorization_url(
            str(integration.id),
            redirect_uri
        )
        
        return OAuth2LoginResponse(
            authorization_url=auth_url,
            state=state
        )
        
    except Exception as e:
        logger.error(f"Error creating OAuth login URL: {e}")
        raise HTTPException(status_code=500, detail="Failed to create authorization URL")


@router.post("/oauth/{provider}/callback")
async def oauth_callback(
    provider: str,
    callback_data: OAuth2CallbackRequest,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_tenant_user)
):
    """Handle OAuth2 callback"""
    provider = provider.upper()
    if provider not in ["FORTNOX", "VISMA"]:
        raise HTTPException(status_code=400, detail="Unsupported OAuth provider")
    
    integration = db.query(InvoiceIntegration).filter(
        InvoiceIntegration.id == callback_data.integration_id,
        InvoiceIntegration.tenant_id == current_user.tenant_id,
        InvoiceIntegration.integration_type == provider
    ).first()
    
    if not integration:
        raise HTTPException(status_code=404, detail="Integration not found")
    
    try:
        oauth_manager = OAuth2Manager(db)
        
        # Default redirect URI
        redirect_uri = f"http://localhost:3000/integrations/oauth/{provider.lower()}/callback"
        
        token = await oauth_manager.handle_callback(
            callback_data.code,
            callback_data.state,
            str(integration.id),
            redirect_uri
        )
        
        return {"message": "OAuth authentication successful", "token_id": str(token.id)}
        
    except Exception as e:
        logger.error(f"Error handling OAuth callback: {e}")
        raise HTTPException(status_code=400, detail=f"OAuth callback failed: {str(e)}")


def _validate_integration_config(integration_type: IntegrationType, config: Dict[str, Any]):
    """Validate integration configuration"""
    if integration_type == IntegrationType.FORTNOX:
        required_fields = ["client_id", "client_secret"]
        for field in required_fields:
            if field not in config:
                raise HTTPException(status_code=400, detail=f"Missing required field: {field}")
    
    elif integration_type == IntegrationType.VISMA:
        required_fields = ["client_id", "client_secret"]
        for field in required_fields:
            if field not in config:
                raise HTTPException(status_code=400, detail=f"Missing required field: {field}")
    
    elif integration_type == IntegrationType.HTTP:
        if "base_url" not in config:
            raise HTTPException(status_code=400, detail="Missing required field: base_url")
        
        # Validate URL format
        from urllib.parse import urlparse
        parsed = urlparse(config["base_url"])
        if not parsed.scheme or not parsed.netloc:
            raise HTTPException(status_code=400, detail="Invalid base_url format")


def _create_fetcher(integration: InvoiceIntegration, db: Session) -> InvoiceFetcher:
    """Create appropriate fetcher instance for integration"""
    config = integration.configuration.copy()
    config["integration_id"] = str(integration.id)
    
    if integration.integration_type == "FORTNOX":
        return FortnoxInvoiceFetcher(str(integration.tenant_id), config, db)
    elif integration.integration_type == "VISMA":
        return VismaInvoiceFetcher(str(integration.tenant_id), config, db)
    elif integration.integration_type == "HTTP":
        return SimpleHttpInvoiceFetcher(str(integration.tenant_id), config)
    else:
        raise ValueError(f"Unsupported integration type: {integration.integration_type}")


@router.post("/{integration_id}/sync", response_model=IntegrationSyncResult)
async def manual_sync_integration(
    integration_id: UUID,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_tenant_user)
):
    """Manually trigger sync for a specific integration"""
    check_permission(current_user, Permission.MANAGE_INTEGRATIONS)

    integration = db.query(InvoiceIntegration).filter(
        InvoiceIntegration.id == integration_id,
        InvoiceIntegration.tenant_id == current_user.tenant_id
    ).first()

    if not integration:
        raise HTTPException(status_code=404, detail="Integration not found")

    if not integration.is_active:
        raise HTTPException(status_code=400, detail="Integration is not active")

    try:
        integration_service = IntegrationService(db)

        # Fetch invoices from this specific integration
        since = None  # Fetch all available invoices
        fetcher = integration_service.create_fetcher(integration)
        invoices = await fetcher.fetch_invoices(since)

        # Update integration sync status
        from datetime import datetime
        integration.last_sync_at = datetime.utcnow()
        integration.last_sync_status = "success"
        integration.last_error = None
        db.commit()

        logger.info(f"Manual sync completed for integration {integration_id}: {len(invoices)} invoices fetched")

        return IntegrationSyncResult(
            integration_id=integration_id,
            integration_type=integration.integration_type,
            success=True,
            invoices_count=len(invoices),
            message=f"Successfully fetched {len(invoices)} invoices",
            synced_at=integration.last_sync_at
        )

    except Exception as e:
        # Update integration with error status
        from datetime import datetime
        integration.last_sync_at = datetime.utcnow()
        integration.last_sync_status = "failed"
        integration.last_error = str(e)
        db.commit()

        logger.error(f"Manual sync failed for integration {integration_id}: {e}")

        return IntegrationSyncResult(
            integration_id=integration_id,
            integration_type=integration.integration_type,
            success=False,
            invoices_count=0,
            message=f"Sync failed: {str(e)}",
            synced_at=integration.last_sync_at
        )


@router.post("/sync-all", response_model=List[IntegrationSyncResult])
async def manual_sync_all_integrations(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_tenant_user)
):
    """Manually trigger sync for all active integrations"""
    check_permission(current_user, Permission.MANAGE_INTEGRATIONS)

    integrations = db.query(InvoiceIntegration).filter(
        InvoiceIntegration.tenant_id == current_user.tenant_id,
        InvoiceIntegration.is_active == True
    ).all()

    if not integrations:
        return []

    results = []
    integration_service = IntegrationService(db)

    for integration in integrations:
        try:
            # Fetch invoices from this integration
            since = None  # Fetch all available invoices
            fetcher = integration_service.create_fetcher(integration)
            invoices = await fetcher.fetch_invoices(since)

            # Update integration sync status
            from datetime import datetime
            integration.last_sync_at = datetime.utcnow()
            integration.last_sync_status = "success"
            integration.last_error = None

            results.append(IntegrationSyncResult(
                integration_id=integration.id,
                integration_type=integration.integration_type,
                success=True,
                invoices_count=len(invoices),
                message=f"Successfully fetched {len(invoices)} invoices",
                synced_at=integration.last_sync_at
            ))

        except Exception as e:
            # Update integration with error status
            from datetime import datetime
            integration.last_sync_at = datetime.utcnow()
            integration.last_sync_status = "failed"
            integration.last_error = str(e)

            results.append(IntegrationSyncResult(
                integration_id=integration.id,
                integration_type=integration.integration_type,
                success=False,
                invoices_count=0,
                message=f"Sync failed: {str(e)}",
                synced_at=integration.last_sync_at
            ))

    db.commit()
    logger.info(f"Manual sync completed for {len(integrations)} integrations")

    return results


@router.post("/trigger-scheduled-sync")
async def trigger_scheduled_sync(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_tenant_user)
):
    """Manually trigger the scheduled sync task"""
    check_permission(current_user, Permission.MANAGE_INTEGRATIONS)

    try:
        # Trigger the Celery task for AI agent invoice fetching
        task = ai_agent_fetch_invoices_task.delay()

        logger.info(f"Scheduled sync task triggered manually by user {current_user.user_id}")

        return {
            "message": "Scheduled sync task triggered successfully",
            "task_id": task.id
        }

    except Exception as e:
        logger.error(f"Failed to trigger scheduled sync task: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to trigger sync: {str(e)}")


@router.get("/schedule", response_model=ScheduleSettings)
async def get_schedule_settings(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_tenant_user)
):
    """Get current schedule settings"""
    check_permission(current_user, Permission.MANAGE_INTEGRATIONS)

    # For now, we'll return the current Celery beat schedule settings
    # In a real implementation, you might want to store these in the database per tenant
    from app.celery_app import celery_app

    schedule_config = celery_app.conf.beat_schedule.get('ai-agent-fetch-invoices', {})

    return ScheduleSettings(
        enabled=bool(schedule_config),
        cron_expression="0 */2 * * *",  # Every 2 hours (default)
        last_run=None,  # Would need to be tracked in database
        next_run=None   # Would need to be calculated
    )


@router.put("/schedule", response_model=ScheduleSettings)
async def update_schedule_settings(
    settings: ScheduleSettingsUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_tenant_user)
):
    """Update schedule settings"""
    check_permission(current_user, Permission.MANAGE_INTEGRATIONS)

    # Note: In a production environment, you would want to:
    # 1. Store schedule settings per tenant in the database
    # 2. Update the Celery beat schedule dynamically
    # 3. Handle cron expression validation

    # For now, we'll just return the updated settings
    # The actual schedule is managed globally in celery_app.py

    current_settings = ScheduleSettings(
        enabled=True,
        cron_expression="0 */2 * * *"
    )

    if settings.enabled is not None:
        current_settings.enabled = settings.enabled

    if settings.cron_expression is not None:
        # Basic validation of cron expression
        try:
            from celery.schedules import crontab
            # Parse the cron expression to validate it
            parts = settings.cron_expression.split()
            if len(parts) == 5:
                minute, hour, day, month, day_of_week = parts
                # This is a simplified validation
                current_settings.cron_expression = settings.cron_expression
            else:
                raise ValueError("Invalid cron expression format")
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"Invalid cron expression: {str(e)}")

    logger.info(f"Schedule settings updated by user {current_user.user_id}: enabled={current_settings.enabled}, cron={current_settings.cron_expression}")

    return current_settings
